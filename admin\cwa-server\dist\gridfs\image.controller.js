"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageController = void 0;
const common_1 = require("@nestjs/common");
const gridfs_service_1 = require("./gridfs.service");
const mongodb_1 = require("mongodb");
let ImageController = class ImageController {
    constructor(gridfsService) {
        this.gridfsService = gridfsService;
    }
    async getImageByFilename(filename, res) {
        try {
            console.log('Image request for filename:', filename);
            if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
                throw new common_1.HttpException({
                    status: 'fail',
                    message: 'Invalid filename',
                }, common_1.HttpStatus.BAD_REQUEST);
            }
            const file = await this.gridfsService.findFileByFilename(filename);
            if (!file) {
                throw new common_1.HttpException({
                    status: 'fail',
                    message: 'Image not found',
                }, common_1.HttpStatus.NOT_FOUND);
            }
            console.log('File found:', file.filename, 'mimetype:', file.metadata?.mimetype);
            res.set({
                'Content-Type': file.metadata?.mimetype || 'image/jpeg',
                'Cache-Control': 'public, max-age=3600',
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': 'DENY'
            });
            const readStream = this.gridfsService.createReadStreamByFilename(filename);
            readStream.on('error', (error) => {
                console.error('GridFS read stream error:', error);
                if (!res.headersSent) {
                    res.status(500).json({
                        status: 'error',
                        message: 'Error streaming image'
                    });
                }
            });
            readStream.pipe(res);
        }
        catch (error) {
            console.error('Error retrieving image:', error);
            if (!res.headersSent) {
                if (error instanceof common_1.HttpException) {
                    res.status(error.getStatus()).json(error.getResponse());
                }
                else {
                    res.status(500).json({
                        status: 'error',
                        message: 'Error retrieving image',
                        error: error.message
                    });
                }
            }
        }
    }
    async getImageById(id, res) {
        try {
            console.log('Image request for ID:', id);
            if (!mongodb_1.ObjectId.isValid(id)) {
                throw new common_1.HttpException({
                    status: 'fail',
                    message: 'Invalid image ID',
                }, common_1.HttpStatus.BAD_REQUEST);
            }
            const file = await this.gridfsService.findFileById(id);
            if (!file) {
                throw new common_1.HttpException({
                    status: 'fail',
                    message: 'Image not found',
                }, common_1.HttpStatus.NOT_FOUND);
            }
            console.log('File found by ID:', file.filename, 'mimetype:', file.metadata?.mimetype);
            res.set({
                'Content-Type': file.metadata?.mimetype || 'image/jpeg',
                'Cache-Control': 'public, max-age=3600',
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': 'DENY'
            });
            const readStream = this.gridfsService.createReadStream(id);
            readStream.on('error', (error) => {
                console.error('GridFS read stream error:', error);
                if (!res.headersSent) {
                    res.status(500).json({
                        status: 'error',
                        message: 'Error streaming image'
                    });
                }
            });
            readStream.pipe(res);
        }
        catch (error) {
            console.error('Error retrieving image:', error);
            if (!res.headersSent) {
                if (error instanceof common_1.HttpException) {
                    res.status(error.getStatus()).json(error.getResponse());
                }
                else {
                    res.status(500).json({
                        status: 'error',
                        message: 'Error retrieving image',
                        error: error.message
                    });
                }
            }
        }
    }
};
exports.ImageController = ImageController;
__decorate([
    (0, common_1.Get)('file/:filename'),
    __param(0, (0, common_1.Param)('filename')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ImageController.prototype, "getImageByFilename", null);
__decorate([
    (0, common_1.Get)('id/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ImageController.prototype, "getImageById", null);
exports.ImageController = ImageController = __decorate([
    (0, common_1.Controller)('images'),
    __metadata("design:paramtypes", [gridfs_service_1.GridFSService])
], ImageController);
//# sourceMappingURL=image.controller.js.map